package main

import (
	"fmt"
	"log"

	"github.com/aman<PERSON>singh77/search_engine/pkg"
)

// DemoTFIDFCalculation demonstrates the TF-IDF calculation with sample data
func DemoTFIDFCalculation() {
	log.Println("=== TF-IDF Calculation Demo ===")

	// Create sample data
	rawTFData := pkg.RawTFData{
		"search":  {1: 3, 2: 1, 3: 2},    // appears in 3 documents
		"engine":  {1: 2, 2: 2},          // appears in 2 documents
		"web":     {1: 1, 3: 1},          // appears in 2 documents
		"crawler": {2: 3},                // appears in 1 document
		"index":   {3: 2},                // appears in 1 document
	}

	docLengths := pkg.DocLengths{
		1: 20, // Document 1 has 20 tokens
		2: 15, // Document 2 has 15 tokens
		3: 10, // Document 3 has 10 tokens
	}

	// Create token ID mappings (simulating database IDs)
	tokenTextToIDMap := map[string]int64{
		"search":  1,
		"engine":  2,
		"web":     3,
		"crawler": 4,
		"index":   5,
	}

	// Create TF-IDF calculator
	calculator := pkg.NewTFIDFCalculator()

	// Step 1: Validate data
	log.Println("Step 1: Validating TF data...")
	err := calculator.ValidateTFData(rawTFData, docLengths)
	if err != nil {
		log.Fatalf("Data validation failed: %v", err)
	}

	// Step 2: Show statistics
	log.Println("Step 2: TF data statistics...")
	stats := calculator.GetTFDataStatistics(rawTFData, docLengths)
	log.Println(stats.String())

	// Step 3: Calculate document frequencies and IDF scores
	log.Println("Step 3: Calculating IDF scores...")
	tokenIDToDocFreq := make(map[int]int)
	for tokenText, docMap := range rawTFData {
		if tokenID, exists := tokenTextToIDMap[tokenText]; exists {
			tokenIDToDocFreq[int(tokenID)] = len(docMap)
		}
	}

	totalDocs := len(docLengths)
	idfScores := pkg.CalculateIDF(tokenIDToDocFreq, totalDocs)

	// Convert to int64 keys for compatibility
	idfScoresInt64 := make(map[int64]float64)
	for tokenID, idf := range idfScores {
		idfScoresInt64[int64(tokenID)] = idf
	}

	// Display IDF scores
	log.Println("IDF Scores:")
	for tokenText, tokenID := range tokenTextToIDMap {
		if idf, exists := idfScoresInt64[tokenID]; exists {
			docFreq := tokenIDToDocFreq[int(tokenID)]
			log.Printf("  %s (ID: %d): IDF = %.4f (appears in %d/%d documents)", 
				tokenText, tokenID, idf, docFreq, totalDocs)
		}
	}

	// Step 4: Calculate TF-IDF scores
	log.Println("Step 4: Calculating TF-IDF scores...")
	tfidfScores, err := calculator.CalculateTFIDFScores(rawTFData, docLengths, tokenTextToIDMap, idfScoresInt64)
	if err != nil {
		log.Fatalf("TF-IDF calculation failed: %v", err)
	}

	// Display results
	log.Println("TF-IDF Scores:")
	log.Println("Token\t\tDoc\tRaw TF\tNorm TF\t\tIDF\t\tTF-IDF")
	log.Println("-----\t\t---\t------\t-------\t\t---\t\t------")

	// Create reverse mapping for display
	idToTokenText := make(map[int]string)
	for text, id := range tokenTextToIDMap {
		idToTokenText[int(id)] = text
	}

	for _, score := range tfidfScores {
		tokenText := idToTokenText[score.TokenID]
		docID := score.DocID
		
		// Get raw TF count
		rawTF := rawTFData[tokenText][docID]
		docLen := docLengths[docID]
		normalizedTF := float64(rawTF) / float64(docLen)
		idf := idfScoresInt64[int64(score.TokenID)]

		fmt.Printf("%-12s\t%d\t%d\t%.4f\t\t%.4f\t\t%.4f\n", 
			tokenText, docID, rawTF, normalizedTF, idf, score.TFIDFScore)
	}

	// Step 5: Demonstrate batch processing
	log.Println("Step 5: Demonstrating batch processing...")
	batchScores, err := calculator.CalculateTFIDFBatch(rawTFData, docLengths, tokenTextToIDMap, idfScoresInt64, 3)
	if err != nil {
		log.Fatalf("Batch TF-IDF calculation failed: %v", err)
	}

	log.Printf("Batch processing produced %d scores (same as regular: %t)", 
		len(batchScores), len(batchScores) == len(tfidfScores))

	// Step 6: Show ranking example
	log.Println("Step 6: Document ranking example for query 'search engine'...")
	queryTerms := []string{"search", "engine"}
	
	// Calculate document scores for the query
	docScores := make(map[int64]float64)
	for _, score := range tfidfScores {
		tokenText := idToTokenText[score.TokenID]
		if contains(queryTerms, tokenText) {
			docScores[score.DocID] += score.TFIDFScore
		}
	}

	log.Println("Document ranking for query 'search engine':")
	for docID, totalScore := range docScores {
		log.Printf("  Document %d: Total TF-IDF Score = %.4f", docID, totalScore)
	}

	log.Println("=== Demo completed successfully ===")
}

// Helper function to check if slice contains string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// RunTFIDFDemo runs the TF-IDF demonstration
func RunTFIDFDemo() {
	log.Println("Running TF-IDF calculation demonstration...")
	DemoTFIDFCalculation()
}
