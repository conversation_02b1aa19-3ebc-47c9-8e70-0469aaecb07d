# TF-IDF Implementation Guide

This document describes the TF-IDF (Term Frequency-Inverse Document Frequency) implementation in the search engine project.

## Overview

The TF-IDF implementation provides a complete pipeline for calculating and storing TF-IDF scores for search ranking. The implementation is modular, testable, and handles edge cases robustly.

## Architecture

### Components

1. **TFIDFService** (`cmd/tfidf_service.go`)
   - Main service orchestrating the TF-IDF calculation pipeline
   - Handles data loading, validation, and storage coordination
   - Provides high-level API for TF-IDF operations

2. **TFIDFCalculator** (`pkg/tfidf.go`)
   - Core mathematical calculations for TF-IDF
   - Modular functions for different calculation steps
   - Batch processing capabilities for memory efficiency

3. **Global Data Access** (`crawler/crawl.go`)
   - Thread-safe access to global TF data structures
   - Data copying to prevent external modification
   - Statistics and validation functions

4. **Database Integration** (`crawler/db.go`)
   - Token processing and ID mapping
   - TF-IDF score storage with conflict resolution
   - Batch database operations for performance

## Usage

### Command Line Interface

```bash
# Run TF-IDF calculation (requires prior crawling)
go run cmd/main.go -mode=tfidf

# Run full pipeline: crawl -> TF-IDF -> search
go run cmd/main.go -mode=full -query="your search terms"

# Run only crawling first
go run cmd/main.go -mode=crawl
```

### Programmatic Usage

```go
// Create TF-IDF service
tfidfService := NewTFIDFService(db)

// Check if TF data is available
if !tfidfService.HasTFData() {
    log.Println("No TF data available, run crawling first")
    return
}

// Run TF-IDF calculation
err := tfidfService.CalculateAndStoreTFIDF()
if err != nil {
    log.Fatalf("TF-IDF calculation failed: %v", err)
}
```

## Implementation Details

### TF-IDF Calculation Pipeline

1. **Data Loading and Validation**
   - Load raw TF data from global crawler structures
   - Validate data consistency between TF data and document lengths
   - Generate statistics about the dataset

2. **Token Processing**
   - Insert/update tokens in the database
   - Create token text to ID mappings
   - Calculate document frequencies for each token

3. **IDF Calculation**
   - Use smoothed IDF formula: `log(1 + (N - df + 0.5) / (df + 0.5))`
   - Handles edge cases (terms in all documents, zero frequencies)
   - Ensures positive IDF values

4. **TF-IDF Score Calculation**
   - Normalized TF: `raw_tf_count / document_length`
   - Final TF-IDF: `normalized_tf * idf`
   - Batch processing for memory efficiency

5. **Database Storage**
   - Upsert TF-IDF scores with conflict resolution
   - Progress logging for large datasets
   - Transaction management for data consistency

### Mathematical Formulas

**Normalized Term Frequency (TF):**
```
TF(t,d) = count(t,d) / |d|
```
Where:
- `count(t,d)` = number of times term t appears in document d
- `|d|` = total number of terms in document d

**Smoothed Inverse Document Frequency (IDF):**
```
IDF(t) = log(1 + (N - df(t) + 0.5) / (df(t) + 0.5))
```
Where:
- `N` = total number of documents
- `df(t)` = number of documents containing term t

**TF-IDF Score:**
```
TF-IDF(t,d) = TF(t,d) × IDF(t)
```

### Data Structures

```go
// Raw term frequency data
type RawTFData map[string]map[int64]int

// Document lengths
type DocLengths map[int64]int

// TF-IDF score record
type TFIDFScore struct {
    TokenID    int     `db:"token_id"`
    DocID      int64   `db:"doc_id"`
    TFIDFScore float64 `db:"tfidf_score"`
}
```

## Features

### Robustness
- **Data Validation**: Comprehensive validation of TF data consistency
- **Error Handling**: Graceful handling of missing data and edge cases
- **Thread Safety**: Safe access to global data structures
- **Transaction Management**: Database operations with proper rollback

### Performance
- **Batch Processing**: Memory-efficient calculation in configurable batches
- **Progress Logging**: Real-time progress updates for large datasets
- **Database Optimization**: Prepared statements and bulk operations
- **Memory Management**: Data copying to prevent memory leaks

### Monitoring
- **Statistics**: Detailed statistics about TF data and calculations
- **Logging**: Comprehensive logging at each pipeline stage
- **Validation**: Pre and post-calculation data validation
- **Metrics**: Token counts, document counts, and score distributions

## Testing

### Unit Tests
```bash
# Run TF-IDF specific tests
go test ./pkg -v -run TestTFIDF

# Run all tests
go test ./... -v
```

### Test Coverage
- Normalized TF calculation with edge cases
- Smoothed IDF calculation with boundary conditions
- Data validation with various input scenarios
- Batch processing with different batch sizes
- Statistics calculation accuracy

## Configuration

### Database Schema
```sql
-- Tokens table
CREATE TABLE tokens (
    id SERIAL PRIMARY KEY,
    token_text TEXT UNIQUE NOT NULL
);

-- Token-Document relationships with TF-IDF scores
CREATE TABLE token_documents (
    token_id INTEGER REFERENCES tokens(id),
    doc_id INTEGER REFERENCES webpages(doc_id),
    tfidf_score FLOAT NOT NULL,
    PRIMARY KEY (token_id, doc_id)
);
```

### Performance Tuning
- **Batch Size**: Adjust batch size based on available memory (default: 1000)
- **Database Connections**: Use connection pooling for concurrent operations
- **Indexing**: Add indexes on frequently queried columns
- **Memory**: Monitor memory usage during large dataset processing

## Troubleshooting

### Common Issues

1. **No TF Data Available**
   - Ensure crawling has been completed first
   - Check global data structures are populated
   - Verify crawler is saving data correctly

2. **Database Connection Errors**
   - Check database configuration
   - Verify database is running and accessible
   - Check connection string format

3. **Memory Issues**
   - Reduce batch size for large datasets
   - Monitor memory usage during processing
   - Consider processing in smaller chunks

4. **Performance Issues**
   - Add database indexes for better query performance
   - Use connection pooling
   - Optimize batch sizes based on system resources

### Debugging

Enable verbose logging to see detailed pipeline execution:
```bash
go run cmd/main.go -mode=tfidf -v
```

Check database state:
```sql
-- Check token count
SELECT COUNT(*) FROM tokens;

-- Check TF-IDF scores
SELECT COUNT(*) FROM token_documents;

-- Check score distribution
SELECT AVG(tfidf_score), MAX(tfidf_score), MIN(tfidf_score) 
FROM token_documents;
```

## Future Enhancements

1. **Incremental Updates**: Support for updating TF-IDF scores without full recalculation
2. **Distributed Processing**: Support for distributed TF-IDF calculation
3. **Alternative Formulas**: Support for different TF-IDF variants (BM25, etc.)
4. **Caching**: In-memory caching of frequently accessed scores
5. **Compression**: Compressed storage for large TF-IDF matrices
