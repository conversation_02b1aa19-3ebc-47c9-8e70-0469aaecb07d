package crawler

import (
	"fmt"
	"log"
	"strings"
	"sync"

	"github.com/PuerkitoBio/goquery"
	"github.com/amankumarsingh77/search_engine/models"
	"github.com/amankumarsingh77/search_engine/pkg"
	"github.com/gocolly/colly"
)

// Global data structures for TF-IDF calculation
// These should be managed carefully for concurrent access
var (
	globalRawTFData  = make(pkg.RawTFData)
	globalDocLengths = make(pkg.DocLengths)
	globalDataMutex  = &sync.Mutex{}
)

type crawler struct {
	collector *colly.Collector
	frontier  URLFrontier
	db        DB
}

type WebCrawler interface {
	Process(url string) (*models.WebPage, error)
}

func NewCrawler(collector *colly.Collector, frontier URLFrontier, db *DB) WebCrawler {
	return &crawler{
		collector: collector,
		frontier:  frontier,
		db:        *db,
	}
}

func (c *crawler) Process(url string) (*models.WebPage, error) {
	var pageData *models.WebPage
	var processingError error

	// Set up HTML processing callback
	c.collector.OnHTML("html", func(e *colly.HTMLElement) {
		doc := e.DOM
		pageURL := e.Request.URL.String()

		title := strings.TrimSpace(doc.Find("title").Text())
		description := strings.TrimSpace(doc.Find("meta[name='description']").AttrOr("content", ""))
		keywordsRaw := strings.TrimSpace(doc.Find("meta[name='keywords']").AttrOr("content", ""))
		var keywords []string
		if keywordsRaw != "" {
			keywords = strings.Split(keywordsRaw, ",")
			for i, k := range keywords {
				keywords[i] = strings.TrimSpace(k)
			}
		}

		var bodyTextBuilder strings.Builder
		doc.Find("h1, h2, h3, h4, h5, h6, p, span, div").Each(func(_ int, s *goquery.Selection) {
			if s.Is("script") || s.Is("style") {
				return
			}
			bodyTextBuilder.WriteString(strings.TrimSpace(s.Text()))
			bodyTextBuilder.WriteString("\n")
		})

		// Extract internal and external links
		var internalLinks, externalLinks []string
		doc.Find("a[href]").Each(func(_ int, s *goquery.Selection) {
			link := e.Request.AbsoluteURL(s.AttrOr("href", ""))
			if link != "" {
				// Simple check for internal vs external links
				if strings.Contains(link, e.Request.URL.Host) {
					internalLinks = append(internalLinks, link)
				} else {
					externalLinks = append(externalLinks, link)
				}
			}
		})

		pageData = &models.WebPage{
			URL:           pageURL,
			Title:         title,
			Description:   description,
			Keywords:      keywords,
			BodyText:      bodyTextBuilder.String(),
			InternalLinks: internalLinks,
			ExternalLinks: externalLinks,
		}

		// Save webpage to DB and get its doc_id
		docID, err := c.db.AddWebpage(*pageData)
		if err != nil {
			log.Printf("Error saving webpage %s: %v", pageURL, err)
			processingError = fmt.Errorf("failed to save webpage: %w", err)
			return
		}

		// Preprocess and add to in-memory TF/DocLength stores
		tokens := normalizePageContent(pageData.Title + " " + pageData.Description + " " + pageData.BodyText)
		if len(tokens) > 0 {
			globalDataMutex.Lock()
			pkg.AddToIndexAndDocLength(globalRawTFData, globalDocLengths, tokens, docID)
			globalDataMutex.Unlock()
		}
	})

	// Set up error handling
	c.collector.OnError(func(r *colly.Response, err error) {
		log.Printf("Error crawling %s: %v (Status: %d)", r.Request.URL.String(), err, r.StatusCode)
		processingError = fmt.Errorf("crawling error: %w", err)
	})

	// Visit the URL
	err := c.collector.Visit(url)
	if err != nil {
		return nil, fmt.Errorf("failed to visit URL %s: %w", url, err)
	}

	// Wait for the collector to finish
	c.collector.Wait()

	// Check for processing errors
	if processingError != nil {
		return pageData, processingError
	}

	// Return the page data (could be nil if no HTML was processed)
	if pageData == nil {
		return nil, fmt.Errorf("no page data extracted from URL: %s", url)
	}

	return pageData, nil
}

// GetGlobalTFData returns a copy of the global TF data structures
// This function is thread-safe and returns copies to prevent external modification
func GetGlobalTFData() (pkg.RawTFData, pkg.DocLengths) {
	globalDataMutex.Lock()
	defer globalDataMutex.Unlock()

	// Create deep copies to prevent external modification
	rawTFCopy := make(pkg.RawTFData)
	for token, docMap := range globalRawTFData {
		rawTFCopy[token] = make(map[int64]int)
		for docID, count := range docMap {
			rawTFCopy[token][docID] = count
		}
	}

	docLengthsCopy := make(pkg.DocLengths)
	for docID, length := range globalDocLengths {
		docLengthsCopy[docID] = length
	}

	return rawTFCopy, docLengthsCopy
}

// ClearGlobalTFData clears the global TF data structures
// This can be useful for testing or when starting fresh
func ClearGlobalTFData() {
	globalDataMutex.Lock()
	defer globalDataMutex.Unlock()

	globalRawTFData = make(pkg.RawTFData)
	globalDocLengths = make(pkg.DocLengths)
}

// GetGlobalTFDataStats returns statistics about the global TF data
func GetGlobalTFDataStats() (int, int) {
	globalDataMutex.Lock()
	defer globalDataMutex.Unlock()

	return len(globalRawTFData), len(globalDocLengths)
}
