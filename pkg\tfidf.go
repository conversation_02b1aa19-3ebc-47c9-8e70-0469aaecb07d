package pkg

import (
	"fmt"
	"log"
	"math"
)

// TFIDFCalculator provides methods for TF-IDF calculations
type TFIDFCalculator struct{}

// NewTFIDFCalculator creates a new TF-IDF calculator instance
func NewTFIDFCalculator() *TFIDFCalculator {
	return &TFIDFCalculator{}
}

// CalculateTFIDFScores calculates TF-IDF scores for all token-document pairs
func (calc *TFIDFCalculator) CalculateTFIDFScores(
	rawTFData RawTFData,
	docLengths DocLengths,
	tokenTextToIDMap map[string]int64,
	idfScores map[int64]float64,
) ([]TFIDFScore, error) {
	
	var tfidfRecords []TFIDFScore
	skippedTokens := 0
	skippedDocs := 0
	
	log.Printf("Calculating TF-IDF scores for %d tokens...", len(rawTFData))
	
	for tokenText, docMap := range rawTFData {
		tokenID, exists := tokenTextToIDMap[tokenText]
		if !exists {
			skippedTokens++
			continue
		}

		idf, exists := idfScores[tokenID]
		if !exists {
			skippedTokens++
			continue
		}

		for docID, rawTFCount := range docMap {
			docLength, exists := docLengths[docID]
			if !exists || docLength == 0 {
				skippedDocs++
				continue
			}

			// Calculate normalized TF (term frequency / document length)
			normalizedTF := float64(rawTFCount) / float64(docLength)
			
			// Calculate final TF-IDF score
			tfidfScore := normalizedTF * idf
			
			// Only store meaningful scores
			if tfidfScore > 0 {
				tfidfRecords = append(tfidfRecords, TFIDFScore{
					TokenID:    int(tokenID),
					DocID:      docID,
					TFIDFScore: tfidfScore,
				})
			}
		}
	}

	if skippedTokens > 0 {
		log.Printf("Warning: Skipped %d tokens due to missing token IDs or IDF scores", skippedTokens)
	}
	if skippedDocs > 0 {
		log.Printf("Warning: Skipped %d document entries due to missing or zero document lengths", skippedDocs)
	}

	if len(tfidfRecords) == 0 {
		return nil, fmt.Errorf("no TF-IDF scores calculated")
	}

	log.Printf("Successfully calculated %d TF-IDF scores", len(tfidfRecords))
	return tfidfRecords, nil
}

// CalculateNormalizedTF calculates normalized term frequency
func (calc *TFIDFCalculator) CalculateNormalizedTF(rawTFCount int, docLength int) float64 {
	if docLength == 0 {
		return 0.0
	}
	return float64(rawTFCount) / float64(docLength)
}

// CalculateSmoothedIDF calculates smoothed IDF to handle edge cases
func (calc *TFIDFCalculator) CalculateSmoothedIDF(docFreq int, totalDocs int) float64 {
	if totalDocs == 0 || docFreq <= 0 {
		return 0.0
	}
	
	// Using smoothed IDF formula: log(1 + (N - df + 0.5) / (df + 0.5))
	// This variant helps ensure positive values and handles terms appearing in many documents
	return math.Log(1 + (float64(totalDocs-docFreq)+0.5)/(float64(docFreq)+0.5))
}

// ValidateTFData validates the consistency of TF data structures
func (calc *TFIDFCalculator) ValidateTFData(rawTFData RawTFData, docLengths DocLengths) error {
	if len(rawTFData) == 0 {
		return fmt.Errorf("raw TF data is empty")
	}
	
	if len(docLengths) == 0 {
		return fmt.Errorf("document lengths data is empty")
	}

	// Check for consistency between TF data and document lengths
	docIDsInTF := make(map[int64]bool)
	for _, docMap := range rawTFData {
		for docID := range docMap {
			docIDsInTF[docID] = true
		}
	}

	missingDocLengths := 0
	for docID := range docIDsInTF {
		if _, exists := docLengths[docID]; !exists {
			missingDocLengths++
		}
	}

	if missingDocLengths > 0 {
		log.Printf("Warning: %d documents in TF data are missing from document lengths", missingDocLengths)
	}

	log.Printf("TF data validation: %d unique tokens, %d documents in TF data, %d document lengths", 
		len(rawTFData), len(docIDsInTF), len(docLengths))
	
	return nil
}

// GetTFDataStatistics returns detailed statistics about TF data
func (calc *TFIDFCalculator) GetTFDataStatistics(rawTFData RawTFData, docLengths DocLengths) TFDataStats {
	stats := TFDataStats{
		UniqueTokens:     len(rawTFData),
		TotalDocuments:   len(docLengths),
		TokenDocPairs:    0,
		AvgTokensPerDoc:  0.0,
		AvgDocsPerToken:  0.0,
	}

	if len(rawTFData) == 0 || len(docLengths) == 0 {
		return stats
	}

	// Calculate token-document pairs and average docs per token
	totalTokenDocPairs := 0
	for _, docMap := range rawTFData {
		totalTokenDocPairs += len(docMap)
	}
	stats.TokenDocPairs = totalTokenDocPairs
	stats.AvgDocsPerToken = float64(totalTokenDocPairs) / float64(len(rawTFData))

	// Calculate average tokens per document
	totalTokensAcrossAllDocs := 0
	for _, docLength := range docLengths {
		totalTokensAcrossAllDocs += docLength
	}
	stats.AvgTokensPerDoc = float64(totalTokensAcrossAllDocs) / float64(len(docLengths))

	return stats
}

// TFDataStats holds statistics about TF data
type TFDataStats struct {
	UniqueTokens     int     `json:"unique_tokens"`
	TotalDocuments   int     `json:"total_documents"`
	TokenDocPairs    int     `json:"token_doc_pairs"`
	AvgTokensPerDoc  float64 `json:"avg_tokens_per_doc"`
	AvgDocsPerToken  float64 `json:"avg_docs_per_token"`
}

// String returns a string representation of TF data statistics
func (stats TFDataStats) String() string {
	return fmt.Sprintf(
		"TF Data Stats: %d unique tokens, %d documents, %d token-doc pairs, %.2f avg tokens/doc, %.2f avg docs/token",
		stats.UniqueTokens, stats.TotalDocuments, stats.TokenDocPairs, stats.AvgTokensPerDoc, stats.AvgDocsPerToken,
	)
}

// CalculateTFIDFBatch calculates TF-IDF scores in batches for memory efficiency
func (calc *TFIDFCalculator) CalculateTFIDFBatch(
	rawTFData RawTFData,
	docLengths DocLengths,
	tokenTextToIDMap map[string]int64,
	idfScores map[int64]float64,
	batchSize int,
) ([]TFIDFScore, error) {
	
	if batchSize <= 0 {
		batchSize = 1000 // Default batch size
	}

	var allTFIDFRecords []TFIDFScore
	currentBatch := make([]TFIDFScore, 0, batchSize)
	processedTokens := 0

	log.Printf("Calculating TF-IDF scores in batches of %d...", batchSize)

	for tokenText, docMap := range rawTFData {
		tokenID, exists := tokenTextToIDMap[tokenText]
		if !exists {
			continue
		}

		idf, exists := idfScores[tokenID]
		if !exists {
			continue
		}

		for docID, rawTFCount := range docMap {
			docLength, exists := docLengths[docID]
			if !exists || docLength == 0 {
				continue
			}

			normalizedTF := float64(rawTFCount) / float64(docLength)
			tfidfScore := normalizedTF * idf
			
			if tfidfScore > 0 {
				currentBatch = append(currentBatch, TFIDFScore{
					TokenID:    int(tokenID),
					DocID:      docID,
					TFIDFScore: tfidfScore,
				})

				// If batch is full, add to results and reset
				if len(currentBatch) >= batchSize {
					allTFIDFRecords = append(allTFIDFRecords, currentBatch...)
					currentBatch = currentBatch[:0] // Reset slice but keep capacity
				}
			}
		}

		processedTokens++
		if processedTokens%100 == 0 {
			log.Printf("Processed %d/%d tokens...", processedTokens, len(rawTFData))
		}
	}

	// Add remaining records from the last batch
	if len(currentBatch) > 0 {
		allTFIDFRecords = append(allTFIDFRecords, currentBatch...)
	}

	if len(allTFIDFRecords) == 0 {
		return nil, fmt.Errorf("no TF-IDF scores calculated")
	}

	log.Printf("Successfully calculated %d TF-IDF scores in batches", len(allTFIDFRecords))
	return allTFIDFRecords, nil
}
