package main

import (
	"fmt"
	"log"

	"github.com/amankumarsingh77/search_engine/crawler"
	"github.com/amankumarsingh77/search_engine/pkg"
)

// TFIDFService handles TF-IDF calculation operations
type TFIDFService struct {
	db         *crawler.DB
	calculator *pkg.TFIDFCalculator
}

// NewTFIDFService creates a new TF-IDF service instance
func NewTFIDFService(db *crawler.DB) *TFIDFService {
	return &TFIDFService{
		db:         db,
		calculator: pkg.NewTFIDFCalculator(),
	}
}

// CalculateAndStoreTFIDF runs the complete TF-IDF calculation pipeline
func (s *TFIDFService) CalculateAndStoreTFIDF() error {
	// Pre-check: Verify TF data is available
	if !s.HasTFData() {
		tokenCount, docCount := s.GetTFDataStats()
		return fmt.Errorf("no TF data available (tokens: %d, documents: %d) - please run crawling first", tokenCount, docCount)
	}

	// Check if TF-IDF data already exists
	hasExisting, existingCount, err := s.HasExistingTFIDFData()
	if err != nil {
		return fmt.Errorf("failed to check existing TF-IDF data: %w", err)
	}

	if hasExisting {
		log.Printf("Found %d existing TF-IDF scores in database", existingCount)
		log.Println("Clearing existing TF-IDF data before recalculation...")
		err = s.ClearExistingTFIDFData()
		if err != nil {
			return fmt.Errorf("failed to clear existing TF-IDF data: %w", err)
		}
	}

	log.Println("Step 1: Loading TF data and document information...")

	// Load raw TF data and document lengths
	rawTFData, docLengths, err := s.loadTFData()
	if err != nil {
		return fmt.Errorf("failed to load TF data: %w", err)
	}

	if len(rawTFData) == 0 {
		return fmt.Errorf("no TF data found - ensure crawling has been completed first")
	}

	log.Printf("Loaded TF data for %d unique tokens across %d documents", len(rawTFData), len(docLengths))

	// Validate TF data consistency
	err = s.calculator.ValidateTFData(rawTFData, docLengths)
	if err != nil {
		return fmt.Errorf("TF data validation failed: %w", err)
	}

	// Log TF data statistics
	stats := s.calculator.GetTFDataStatistics(rawTFData, docLengths)
	log.Println(stats.String())

	// Step 2: Process tokens and calculate document frequencies
	log.Println("Step 2: Processing tokens and calculating document frequencies...")
	tokenTextToIDMap, tokenIDToDocFreq, totalDocs, err := s.processTokensAndCalculateDocFreq(rawTFData)
	if err != nil {
		return fmt.Errorf("failed to process tokens: %w", err)
	}

	log.Printf("Processed %d unique tokens for %d total documents", len(tokenTextToIDMap), totalDocs)

	// Step 3: Calculate IDF scores
	log.Println("Step 3: Calculating IDF scores...")
	idfScores := s.calculateIDFScores(tokenIDToDocFreq, totalDocs)
	log.Printf("Calculated IDF scores for %d tokens", len(idfScores))

	// Step 4: Calculate and store TF-IDF scores
	log.Println("Step 4: Calculating and storing TF-IDF scores...")
	err = s.calculateAndStoreTFIDFScores(rawTFData, docLengths, tokenTextToIDMap, idfScores)
	if err != nil {
		return fmt.Errorf("failed to calculate and store TF-IDF scores: %w", err)
	}

	log.Println("TF-IDF calculation pipeline completed successfully")
	return nil
}

// loadTFData loads raw term frequency data and document lengths
// This function attempts to load from global crawler data structures
func (s *TFIDFService) loadTFData() (pkg.RawTFData, pkg.DocLengths, error) {
	// Try to access global data from crawler package
	rawTFData, docLengths := s.getGlobalTFData()
	
	if len(rawTFData) == 0 {
		// If no global data available, try to reconstruct from database
		log.Println("No global TF data found, attempting to reconstruct from database...")
		return s.reconstructTFDataFromDB()
	}

	return rawTFData, docLengths, nil
}

// getGlobalTFData attempts to access global TF data from the crawler package
func (s *TFIDFService) getGlobalTFData() (pkg.RawTFData, pkg.DocLengths) {
	// Access global TF data from the crawler package
	rawTFData, docLengths := crawler.GetGlobalTFData()

	// Log statistics about the loaded data
	tokenCount, docCount := len(rawTFData), len(docLengths)
	if tokenCount > 0 && docCount > 0 {
		log.Printf("Loaded global TF data: %d unique tokens, %d documents", tokenCount, docCount)
	} else {
		log.Println("No global TF data available")
	}

	return rawTFData, docLengths
}

// reconstructTFDataFromDB reconstructs TF data from the database
// This is a fallback method when global data structures aren't available
func (s *TFIDFService) reconstructTFDataFromDB() (pkg.RawTFData, pkg.DocLengths, error) {
	// This would require additional database queries to reconstruct TF data
	// For now, return an error indicating that crawling should be done first
	return nil, nil, fmt.Errorf("TF data reconstruction from database not implemented - please run crawling first")
}

// HasTFData checks if TF data is available for processing
func (s *TFIDFService) HasTFData() bool {
	tokenCount, docCount := crawler.GetGlobalTFDataStats()
	return tokenCount > 0 && docCount > 0
}

// GetTFDataStats returns statistics about available TF data
func (s *TFIDFService) GetTFDataStats() (int, int) {
	return crawler.GetGlobalTFDataStats()
}

// HasExistingTFIDFData checks if TF-IDF scores already exist in the database
func (s *TFIDFService) HasExistingTFIDFData() (bool, int, error) {
	var count int
	err := s.db.GetConnection().Get(&count, "SELECT COUNT(*) FROM token_documents")
	if err != nil {
		return false, 0, fmt.Errorf("failed to check existing TF-IDF data: %w", err)
	}
	return count > 0, count, nil
}

// ClearExistingTFIDFData removes all existing TF-IDF scores from the database
func (s *TFIDFService) ClearExistingTFIDFData() error {
	_, err := s.db.GetConnection().Exec("DELETE FROM token_documents")
	if err != nil {
		return fmt.Errorf("failed to clear existing TF-IDF data: %w", err)
	}
	log.Println("Cleared all existing TF-IDF data from database")
	return nil
}

// processTokensAndCalculateDocFreq processes tokens in the database and calculates document frequencies
func (s *TFIDFService) processTokensAndCalculateDocFreq(rawTFData pkg.RawTFData) (map[string]int64, map[int64]int, int, error) {
	// Get total number of documents
	totalDocs := s.getTotalDocumentCount(rawTFData)
	
	// Process tokens using the existing database method
	tokenTextToIDMap, tokenIDToDocFreq, err := s.db.ProcessTokensInDB(s.db.GetConnection(), rawTFData)
	if err != nil {
		return nil, nil, 0, fmt.Errorf("failed to process tokens in database: %w", err)
	}

	return tokenTextToIDMap, tokenIDToDocFreq, totalDocs, nil
}

// getTotalDocumentCount calculates the total number of unique documents
func (s *TFIDFService) getTotalDocumentCount(rawTFData pkg.RawTFData) int {
	docSet := make(map[int64]bool)
	for _, docMap := range rawTFData {
		for docID := range docMap {
			docSet[docID] = true
		}
	}
	return len(docSet)
}

// calculateIDFScores calculates IDF scores for all tokens
func (s *TFIDFService) calculateIDFScores(tokenIDToDocFreq map[int64]int, totalDocs int) map[int64]float64 {
	// Convert map[int64]int to map[int]int for compatibility with existing function
	convertedDocFreq := make(map[int]int)
	for tokenID, docFreq := range tokenIDToDocFreq {
		convertedDocFreq[int(tokenID)] = docFreq
	}

	// Use existing IDF calculation function
	idfScores := pkg.CalculateIDF(convertedDocFreq, totalDocs)
	
	// Convert back to map[int64]float64
	result := make(map[int64]float64)
	for tokenID, idf := range idfScores {
		result[int64(tokenID)] = idf
	}
	
	return result
}

// calculateAndStoreTFIDFScores calculates final TF-IDF scores and stores them in the database
func (s *TFIDFService) calculateAndStoreTFIDFScores(rawTFData pkg.RawTFData, docLengths pkg.DocLengths,
	tokenTextToIDMap map[string]int64, idfScores map[int64]float64) error {

	// Use the calculator to compute TF-IDF scores in batches for better memory management
	tfidfRecords, err := s.calculator.CalculateTFIDFBatch(rawTFData, docLengths, tokenTextToIDMap, idfScores, 1000)
	if err != nil {
		return fmt.Errorf("failed to calculate TF-IDF scores: %w", err)
	}

	log.Printf("Calculated %d TF-IDF scores, storing in database...", len(tfidfRecords))

	// Store TF-IDF scores in database using existing function
	err = crawler.StoreTFIDFRecordsInDB(s.db.GetConnection(), tfidfRecords)
	if err != nil {
		return fmt.Errorf("failed to store TF-IDF records: %w", err)
	}

	log.Printf("Successfully stored %d TF-IDF scores in database", len(tfidfRecords))
	return nil
}
