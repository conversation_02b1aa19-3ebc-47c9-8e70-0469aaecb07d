package pkg

import (
	"testing"
)

func TestTFIDFCalculator_CalculateNormalizedTF(t *testing.T) {
	calc := NewTFIDFCalculator()

	tests := []struct {
		name        string
		rawTFCount  int
		docLength   int
		expected    float64
	}{
		{
			name:       "Normal case",
			rawTFCount: 3,
			docLength:  10,
			expected:   0.3,
		},
		{
			name:       "Zero document length",
			rawTFCount: 5,
			docLength:  0,
			expected:   0.0,
		},
		{
			name:       "Zero term frequency",
			rawTFCount: 0,
			docLength:  10,
			expected:   0.0,
		},
		{
			name:       "Single term document",
			rawTFCount: 1,
			docLength:  1,
			expected:   1.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calc.CalculateNormalizedTF(tt.rawTFCount, tt.docLength)
			if result != tt.expected {
				t.<PERSON>rf("CalculateNormalizedTF() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestTFIDFCalculator_CalculateSmoothedIDF(t *testing.T) {
	calc := NewTFIDFCalculator()

	tests := []struct {
		name      string
		docFreq   int
		totalDocs int
		expectPos bool // Whether we expect a positive result
	}{
		{
			name:      "Normal case",
			docFreq:   2,
			totalDocs: 10,
			expectPos: true,
		},
		{
			name:      "Term in all documents",
			docFreq:   10,
			totalDocs: 10,
			expectPos: true, // Smoothed IDF should still be positive
		},
		{
			name:      "Zero total documents",
			docFreq:   1,
			totalDocs: 0,
			expectPos: false,
		},
		{
			name:      "Zero document frequency",
			docFreq:   0,
			totalDocs: 10,
			expectPos: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := calc.CalculateSmoothedIDF(tt.docFreq, tt.totalDocs)
			if tt.expectPos && result <= 0 {
				t.Errorf("CalculateSmoothedIDF() = %v, expected positive value", result)
			}
			if !tt.expectPos && result != 0 {
				t.Errorf("CalculateSmoothedIDF() = %v, expected zero", result)
			}
		})
	}
}

func TestTFIDFCalculator_ValidateTFData(t *testing.T) {
	calc := NewTFIDFCalculator()

	tests := []struct {
		name        string
		rawTFData   RawTFData
		docLengths  DocLengths
		expectError bool
	}{
		{
			name: "Valid data",
			rawTFData: RawTFData{
				"word1": {1: 2, 2: 1},
				"word2": {1: 1, 3: 2},
			},
			docLengths: DocLengths{
				1: 10,
				2: 5,
				3: 8,
			},
			expectError: false,
		},
		{
			name:        "Empty TF data",
			rawTFData:   RawTFData{},
			docLengths:  DocLengths{1: 10},
			expectError: true,
		},
		{
			name: "Empty doc lengths",
			rawTFData: RawTFData{
				"word1": {1: 2},
			},
			docLengths:  DocLengths{},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := calc.ValidateTFData(tt.rawTFData, tt.docLengths)
			if tt.expectError && err == nil {
				t.Errorf("ValidateTFData() expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidateTFData() unexpected error: %v", err)
			}
		})
	}
}

func TestTFIDFCalculator_GetTFDataStatistics(t *testing.T) {
	calc := NewTFIDFCalculator()

	rawTFData := RawTFData{
		"word1": {1: 2, 2: 1},    // appears in 2 docs
		"word2": {1: 1, 3: 2},   // appears in 2 docs
		"word3": {2: 3},         // appears in 1 doc
	}
	docLengths := DocLengths{
		1: 10, // 10 tokens
		2: 5,  // 5 tokens
		3: 8,  // 8 tokens
	}

	stats := calc.GetTFDataStatistics(rawTFData, docLengths)

	// Check basic counts
	if stats.UniqueTokens != 3 {
		t.Errorf("Expected 3 unique tokens, got %d", stats.UniqueTokens)
	}
	if stats.TotalDocuments != 3 {
		t.Errorf("Expected 3 total documents, got %d", stats.TotalDocuments)
	}
	if stats.TokenDocPairs != 5 {
		t.Errorf("Expected 5 token-doc pairs, got %d", stats.TokenDocPairs)
	}

	// Check averages
	expectedAvgTokensPerDoc := (10.0 + 5.0 + 8.0) / 3.0
	if stats.AvgTokensPerDoc != expectedAvgTokensPerDoc {
		t.Errorf("Expected avg tokens per doc %.2f, got %.2f", expectedAvgTokensPerDoc, stats.AvgTokensPerDoc)
	}

	expectedAvgDocsPerToken := 5.0 / 3.0 // 5 token-doc pairs / 3 tokens
	if stats.AvgDocsPerToken != expectedAvgDocsPerToken {
		t.Errorf("Expected avg docs per token %.2f, got %.2f", expectedAvgDocsPerToken, stats.AvgDocsPerToken)
	}
}

func TestTFIDFCalculator_CalculateTFIDFScores(t *testing.T) {
	calc := NewTFIDFCalculator()

	// Setup test data
	rawTFData := RawTFData{
		"word1": {1: 2, 2: 1},
		"word2": {1: 1},
	}
	docLengths := DocLengths{
		1: 10,
		2: 5,
	}
	tokenTextToIDMap := map[string]int64{
		"word1": 1,
		"word2": 2,
	}
	idfScores := map[int64]float64{
		1: 0.5,
		2: 1.0,
	}

	scores, err := calc.CalculateTFIDFScores(rawTFData, docLengths, tokenTextToIDMap, idfScores)
	if err != nil {
		t.Fatalf("CalculateTFIDFScores() error: %v", err)
	}

	if len(scores) != 3 {
		t.Errorf("Expected 3 TF-IDF scores, got %d", len(scores))
	}

	// Verify specific scores
	expectedScores := map[string]float64{
		"token1_doc1": (2.0 / 10.0) * 0.5, // word1 in doc1
		"token1_doc2": (1.0 / 5.0) * 0.5,  // word1 in doc2
		"token2_doc1": (1.0 / 10.0) * 1.0, // word2 in doc1
	}

	for _, score := range scores {
		var key string
		if score.TokenID == 1 && score.DocID == 1 {
			key = "token1_doc1"
		} else if score.TokenID == 1 && score.DocID == 2 {
			key = "token1_doc2"
		} else if score.TokenID == 2 && score.DocID == 1 {
			key = "token2_doc1"
		} else {
			t.Errorf("Unexpected token-doc pair: token %d, doc %d", score.TokenID, score.DocID)
			continue
		}

		expected := expectedScores[key]
		if score.TFIDFScore != expected {
			t.Errorf("Score for %s: expected %.4f, got %.4f", key, expected, score.TFIDFScore)
		}
	}
}

func TestTFIDFCalculator_CalculateTFIDFBatch(t *testing.T) {
	calc := NewTFIDFCalculator()

	// Setup test data
	rawTFData := RawTFData{
		"word1": {1: 2, 2: 1},
		"word2": {1: 1},
		"word3": {2: 3},
	}
	docLengths := DocLengths{
		1: 10,
		2: 5,
	}
	tokenTextToIDMap := map[string]int64{
		"word1": 1,
		"word2": 2,
		"word3": 3,
	}
	idfScores := map[int64]float64{
		1: 0.5,
		2: 1.0,
		3: 0.8,
	}

	// Test with small batch size
	scores, err := calc.CalculateTFIDFBatch(rawTFData, docLengths, tokenTextToIDMap, idfScores, 2)
	if err != nil {
		t.Fatalf("CalculateTFIDFBatch() error: %v", err)
	}

	if len(scores) != 4 {
		t.Errorf("Expected 4 TF-IDF scores, got %d", len(scores))
	}

	// Verify that batch processing gives same results as regular processing
	regularScores, err := calc.CalculateTFIDFScores(rawTFData, docLengths, tokenTextToIDMap, idfScores)
	if err != nil {
		t.Fatalf("CalculateTFIDFScores() error: %v", err)
	}

	if len(scores) != len(regularScores) {
		t.Errorf("Batch and regular processing produced different number of scores: %d vs %d", len(scores), len(regularScores))
	}
}
