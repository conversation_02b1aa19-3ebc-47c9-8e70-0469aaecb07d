package main

import (
	"context"
	"encoding/csv"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/amankumarsingh77/search_engine/config"
	"github.com/amankumarsingh77/search_engine/crawler"
	"github.com/amankumarsingh77/search_engine/models"
	"github.com/gocolly/colly"
)

func main() {
	// Command line flags
	var (
		configFile = flag.String("config", "crawler.yaml", "Path to configuration file")
		mode       = flag.String("mode", "crawl", "Mode: crawl, tfidf, search, seed or full")
		query      = flag.String("query", "", "Search query (for search mode)")
		workers    = flag.Int("workers", 3, "Number of worker goroutines")
		seedFile   = flag.String("seedFile", "seed_urls.csv", "Path to seed URLs file")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadCrawlerConfig()
	if err != nil {
		log.Printf("Failed to load configuration from %s: %v", *configFile, err)
		log.Println("Using default configuration...")
		cfg = getDefaultConfig()
	}

	// Override workers from command line
	if *workers > 0 {
		cfg.Workers = *workers
	}

	// Setup context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigChan
		log.Println("Received shutdown signal, gracefully shutting down...")
		cancel()
	}()

	// Initialize database connection
	db, err := crawler.NewDBConnection(&cfg.DB)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	// Note: DB struct doesn't have a Close method, connection is managed internally

	// Initialize Redis client for URL frontier
	redisClient, err := crawler.NewRedisClient(ctx, &cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()

	// Initialize Bloom filter for URL deduplication
	bloomFilter, err := crawler.NewRedisBloomFilter(ctx, &cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to initialize Bloom filter: %v", err)
	}

	// Initialize URL frontier
	frontier := crawler.NewURLFrontier(redisClient, bloomFilter)
	defer frontier.Close()

	switch *mode {
	case "crawl":
		runCrawler(ctx, cfg, db, frontier)
	case "seed":
		seedUrls(frontier, *seedFile)
	case "tfidf":
		runTFIDFCalculation(db)
	case "search":
		if *query == "" {
			log.Fatal("Search query is required for search mode. Use -query flag.")
		}
		runSearch(db, *query)
	case "full":
		runFullPipeline(ctx, cfg, db, frontier, *seedFile, *query)
	default:
		log.Fatalf("Unknown mode: %s. Use crawl, tfidf, search, or full", *mode)
	}
}

func seedUrls(frontier crawler.URLFrontier, seedFile string) {
	urls, err := loadSeedURLs(seedFile)
	ctx := context.Background()
	log.Println("Seeding urls to the queue")
	if err != nil {
		log.Println(err)
	}
	for _, url := range urls {
		if err = frontier.Add(ctx, url); err != nil {
			log.Printf("skipping url %s : error : %v", url, err)
		}
	}

}

func runCrawler(ctx context.Context, cfg *config.CrawlerConfig, db *crawler.DB, frontier crawler.URLFrontier) {
	// Create a cancellable context
	crawlCtx, cancel := context.WithCancel(ctx)
	defer cancel() // Ensure cancellation on function exit

	log.Println("Starting crawler...")

	collector := colly.NewCollector(
		colly.Async(true),
		colly.UserAgent("GoTFIDFSearchCrawler/1.0 (+http://example.com/bot)"),
		colly.IgnoreRobotsTxt(),
		colly.MaxDepth(cfg.MaxDepth),
	)

	err := collector.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: 2,
		Delay:       1 * time.Second,
		RandomDelay: 1 * time.Second,
	})
	if err != nil {
		log.Fatalf("Failed to set rate limiting: %v", err)
	}

	webCrawler := crawler.NewCrawler(collector, frontier, db)

	pagesChan := make(chan models.WebPage, 100)

	// WaitGroup for worker coordination
	var wg sync.WaitGroup
	workers := make([]*crawler.Worker, cfg.Workers)

	for i := 0; i < cfg.Workers; i++ {
		workerID := fmt.Sprintf("worker-%d", i)
		logger := log.New(os.Stdout, fmt.Sprintf("[%s] ", workerID), log.LstdFlags)

		workers[i] = crawler.NewWorker(workerID, frontier, pagesChan, logger, webCrawler)

		wg.Add(1)
		go workers[i].Start(crawlCtx, &wg)
	}

	// Monitor crawled pages
	go func() {
		pageCount := 0
		for page := range pagesChan {
			pageCount++
			if page.ErrorString != "" {
				log.Printf("Page crawled with error: %s - %s", page.URL, page.ErrorString)
			} else {
				log.Printf("Page crawled successfully: %s (Title: %s)", page.URL, page.Title)
			}
			if pageCount%10 == 0 {
				log.Printf("Crawled %d pages so far...", pageCount)
			}
		}
		log.Println("Pages channel closed, monitor exiting")
	}()

	log.Printf("Started %d workers. Crawling in progress...", cfg.Workers)

	// Monitor timeout, cancellation or worker completion
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(pagesChan) // Close after all workers done
		close(done)
	}()

	select {
	case <-crawlCtx.Done():
		log.Println("Crawling cancelled")
	case <-done:
		log.Println("All workers finished")
	case <-time.After(30 * time.Minute):
		log.Println("Crawling timeout. Cancelling...")
		cancel()
		<-done // Wait for cleanup
	}

	// Ensure all workers are stopped gracefully
	for _, worker := range workers {
		worker.Stop()
	}
	log.Println("Crawling run complete")
}

func runTFIDFCalculation(db *crawler.DB) {
	log.Println("Starting TF-IDF calculation...")

	// In a real implementation, you would need to:
	// 1. Load raw TF data from the database or global structures
	// 2. Calculate document frequencies
	// 3. Compute IDF scores
	// 4. Calculate and store TF-IDF scores

	// For now, we'll use the existing global data structures from the crawler package
	// This is a simplified version - in production you'd want better data management

	log.Println("Note: TF-IDF calculation requires integration with crawler global data")
	log.Println("TF-IDF calculation completed")
}

func runSearch(db *crawler.DB, query string) {
	log.Printf("Searching for: '%s'", query)

	// Implement search functionality here
	// This would use the TF-IDF scores to rank results

	// For now, just log that search functionality needs to be implemented
	log.Println("Search functionality requires:")
	log.Println("1. Query preprocessing (tokenization, stemming)")
	log.Println("2. Token ID lookup in database")
	log.Println("3. TF-IDF score aggregation")
	log.Println("4. Result ranking and presentation")

	log.Println("Search completed")
}

func runFullPipeline(ctx context.Context, cfg *config.CrawlerConfig, db *crawler.DB, frontier crawler.URLFrontier, seedFile, query string) {
	log.Println("Running full pipeline: crawl -> TF-IDF -> search")

	// Run crawler
	runCrawler(ctx, cfg, db, frontier)

	// Run TF-IDF calculation
	runTFIDFCalculation(db)

	// Run search if query provided
	if query != "" {
		runSearch(db, query)
	}
}

func getDefaultConfig() *config.CrawlerConfig {
	return &config.CrawlerConfig{
		MaxDepth: 1,
		Workers:  3,
		Redis: config.RedisConfig{
			Host:     "localhost:6379",
			Port:     6379,
			Local:    true,
			SSL:      false,
			URLQueue: "url_queue",
		},
		DB: config.PostgresConfig{
			Host:     "localhost",
			Port:     5433,
			User:     "admin",
			Password: "secret",
			DBName:   "inverted_index_db",
			SSL:      false,
			Local:    true,
		},
	}
}

func loadSeedURLs(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read seed file %v", err)
	}
	defer file.Close()
	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to read seed file %v", err)
	}
	if len(records) == 0 {
		return nil, fmt.Errorf("seed file is empty")
	}
	var domainIDX = -1
	header := records[0]
	for i, col := range header {
		if col == "Domain" {
			domainIDX = i
			break
		}
	}
	if domainIDX == -1 {
		return nil, fmt.Errorf("failed to find the domain col in seed file")
	}
	var urls []string
	for _, row := range records[1:] {
		if len(row) > domainIDX {
			urls = append(urls, row[domainIDX])
		}
	}
	return urls, nil
}
